# 便签标题动态适应机制总结

## 1. 标题区域的动态适应机制

### 1.1 响应式字体缩放

便签系统使用CSS变量和JavaScript计算相结合的方式，根据画布缩放级别动态调整标题字体大小：

```typescript
// 字体大小根据画布缩放级别动态计算
const fontStyles = useMemo(() => {
  const styles = getFontSizeStyles(canvasScale);
  return {
    ...styles,
    "--note-content-font-size": styles.fontSize,
    "--note-title-font-size": styles.fontSize, // CSS变量控制标题字体大小
  } as React.CSSProperties;
}, [canvasScale]);

// 基础字体大小计算
export const getTitleFontSize = (scale: number): number => {
  return calculateFontSize(14, scale); // 基础标题字体大小 14px
};
```

### 1.2 便签调整大小时的标题适应

头部容器使用弹性布局确保标题区域能够根据便签大小自动调整：

```css
.sticky-note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  min-height: 40px; /* 确保有足够的高度 */
  position: relative;
  flex-wrap: nowrap;
  gap: 8px; /* 标题区域和按钮区域之间的间距 */
}
```

## 2. 标题文本长度的动态适应

### 2.1 标题最大宽度计算

系统实时计算便签宽度，为标题分配最大可用空间，确保控制按钮区域不被挤压：

```typescript
// 动态计算标题最大可用宽度
const getTitleMaxWidth = () => {
  const controlsWidth = 56; // 按钮区域宽度
  const headerPadding = 32; // 头部左右padding (16px * 2)
  const gap = 8; // 标题和按钮之间的间距
  const margin = 10; // 额外边距
  
  const noteWidth = noteRef.current?.offsetWidth || 200;
  const maxAvailableWidth = noteWidth - controlsWidth - headerPadding - gap - margin;
  
  return Math.max(maxAvailableWidth, 80) + "px"; // 至少80px
};
```

### 2.2 标题文本溢出处理

使用CSS属性处理长标题的显示，确保文本不会破坏布局：

```css
.sticky-note-title {
  margin: 0;
  font-size: var(--note-title-font-size, 14px);
  font-weight: 600;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  min-width: 0;
  max-width: 100%; /* 允许占满可用空间 */
  overflow: hidden;
  text-overflow: ellipsis; /* 长文本显示省略号 */
  white-space: nowrap; /* 禁止换行 */
  box-sizing: border-box;
}
```

## 3. 编辑模式与显示模式的切换

### 3.1 标题编辑状态的渲染

编辑模式和显示模式使用相同的宽度限制逻辑，确保一致的用户体验：

```tsx
{isTitleEditing ? (
  <input
    ref={titleInputRef}
    type="text"
    value={localTitle}
    onChange={handleTitleChange}
    className="sticky-note-title-input"
    placeholder="便签标题"
    style={{
      width: "100%", // 占满可用空间
      maxWidth: getTitleMaxWidth(), // 与显示模式保持一致
    }}
  />
) : (
  <h3
    className="sticky-note-title"
    style={{
      backgroundColor: "rgba(0, 0, 0, 0.06)",
      maxWidth: getTitleMaxWidth(), // 使用计算的最大宽度
      display: "inline-block",
    }}
  >
    {localTitle || "便签"}
  </h3>
)}
```

### 3.2 标题输入框的样式适配

输入框样式与显示状态保持一致，确保无缝切换：

```css
.sticky-note-title-input {
  margin: 0;
  font-size: var(--note-title-font-size, 14px);
  font-weight: 600;
  color: #374151;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 6px;
  outline: none;
  width: 100%;
  min-width: 100px;
  max-width: calc(100% - 10px);
  box-sizing: border-box;
}
```

## 4. 不同缩放级别的特殊处理

### 4.1 小缩放级别的适配

为极小缩放级别提供特殊的布局规则，确保标题仍然可读和可操作：

```css
/* 50%和75%缩放级别 */
.sticky-note[data-scale="0.5"] .sticky-note-header,
.sticky-note[data-scale="0.75"] .sticky-note-header {
  position: relative !important;
  padding-right: 36px !important; /* 为控件留出空间 */
}

/* 25%缩放级别的极小便签 */
.sticky-note[data-scale="0.25"] .sticky-note-header {
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  padding: 6px 8px !important;
  min-height: 24px !important;
  overflow: visible !important;
}
```

### 4.2 缩放级别数据属性

通过数据属性标记缩放级别，便于CSS选择器精确控制：

```tsx
<div
  data-scale={canvasScale.toString()} // 添加缩放级别数据属性
  className={`sticky-note color-${note.color} ${isEditing ? "editing" : ""}`}
  style={{
    ...fontStyles, // 应用基于缩放的字体样式
  }}
>
```

## 5. 布局容器的弹性设计

### 5.1 拖拽区域和标题容器

使用弹性布局确保标题区域能够自适应并正确分配空间：

```tsx
<div className="drag-handle" style={{ flexGrow: 1 }}>
  <div style={{
    flex: 1,
    display: "flex",
    justifyContent: "flex-start",
    minWidth: 0, // 允许flex子元素收缩
    overflow: "hidden", // 防止内容溢出
  }}>
    {/* 标题内容 */}
  </div>
</div>
```

### 5.2 控制按钮区域的固定布局

确保控制按钮区域不会被标题挤压：

```css
.sticky-note-controls {
  display: flex;
  gap: 4px;
  justify-content: flex-end;
  flex-shrink: 0; /* 防止按钮被压缩 */
  position: relative;
  min-width: fit-content;
  align-items: center;
  width: auto;
  height: auto;
  box-sizing: border-box;
}
```

## 核心实现策略

### ✅ 响应式设计原则
- **CSS变量驱动**：使用`--note-title-font-size`等CSS变量统一控制字体大小
- **JavaScript计算**：动态计算可用空间，实现精确的布局控制
- **弹性布局**：使用Flexbox确保组件在不同尺寸下的正确表现

### ✅ 文本处理策略
- **溢出省略**：长标题自动显示省略号，保持布局整洁
- **最小宽度保证**：确保标题至少有80px的显示空间
- **编辑一致性**：编辑模式和显示模式使用相同的宽度限制

### ✅ 缩放适配机制
- **数据属性标记**：通过`data-scale`属性为不同缩放级别提供专门样式
- **阶梯式适配**：为0.25x、0.5x、0.75x等关键缩放点提供特殊处理
- **最小可用性保证**：即使在最小缩放下也确保基本功能可用

### ✅ 性能优化
- **useMemo缓存**：缓存字体样式计算结果，避免重复计算
- **像素对齐**：使用`getPixelAlignedValue`确保渲染清晰度
- **防抖更新**：标题编辑使用防抖机制，减少不必要的更新

## 相关文件

- **主要组件**：`src/components/notes/StickyNote.tsx`
- **样式文件**：`src/components/notes/StickyNote.css`
- **工具函数**：`src/utils/fontScaleUtils.ts`
- **常量定义**：`src/components/canvas/CanvasConstants.ts`

## 总结

这套完整的动态适应机制确保了便签标题在任何尺寸、缩放级别和使用场景下都能提供最佳的用户体验，同时保持代码的可维护性和性能表现。通过响应式字体缩放、动态宽度计算、文本溢出处理、弹性布局设计等多重策略，实现了标题区域的智能适应功能。
