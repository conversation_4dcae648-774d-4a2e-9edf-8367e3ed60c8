import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: "./", // Electron 需要相对路径
  server: {
    port: 5173,
    host: "0.0.0.0", // 允许外部访问，便于内网测试
    strictPort: true, // 如果端口被占用，会报错而不是尝试其他端口
  },
  preview: {
    port: 4173,
    host: "0.0.0.0", // 生产预览也允许外部访问
    strictPort: true,
  },
  build: {
    outDir: "dist",
    sourcemap: false, // 生产环境不生成 sourcemap
    minify: "esbuild", // 使用 esbuild 压缩
    target: "esnext", // 使用现代 JS 语法，减小包体积
    chunkSizeWarningLimit: 1000, // 提高警告阈值
    rollupOptions: {
      // 多页面应用配置
      input: {
        main: resolve(__dirname, "index.html"),
        app: resolve(__dirname, "app.html"),
        landing: resolve(__dirname, "landing.html"),
      },
      output: {
        // 分包策略，优化加载性能
        manualChunks: {
          vendor: ["react", "react-dom"],
          antd: ["antd", "@ant-design/icons"],
          tiptap: ["@tiptap/react", "@tiptap/starter-kit"],
          markdown: ["react-markdown", "remark-gfm", "rehype-raw"],
          utils: ["lodash", "uuid", "zustand"],
        },
        // 压缩文件名
        entryFileNames: "[name]-[hash:8].js",
        chunkFileNames: "[name]-[hash:8].js",
        assetFileNames: "[name]-[hash:8].[ext]",
      },
      // 外部化大型依赖（如果不需要打包）
      external: [],
    },
    // 启用 CSS 代码分割
    cssCodeSplit: true,
    // 资源内联阈值，小于此大小的资源会被内联
    assetsInlineLimit: 4096,
  },
  // 优化依赖处理
  optimizeDeps: {
    exclude: ["leader-line"], // 排除 leader-line 的预构建，使用静态文件
  },
  // 解决兼容性问题
  define: {
    global: "globalThis",
  },
});
