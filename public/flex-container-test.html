<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flex布局容器稳定性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .test-note {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin: 20px 0;
            background: #fbbf24;
            font-size: 14px;
            position: relative;
        }
        
        .test-note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            flex-wrap: nowrap;
            gap: 8px;
            min-height: 40px;
            position: relative;
            /* 重要：给容器一个明确的边界 */
            border: 2px dashed rgba(255, 0, 0, 0.3);
            box-sizing: border-box;
        }
        
        .test-title-container {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 0;
            overflow: hidden;
            max-width: 100%;
            /* 重要：给标题容器一个明确的边界 */
            border: 2px dashed rgba(0, 255, 0, 0.3);
            box-sizing: border-box;
        }
        
        /* 修复后的flex布局 - 编辑状态 */
        .test-title-input-fixed {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            background: rgba(0, 0, 0, 0.06);
            border: none;
            border-radius: 4px;
            padding: 2px 8px;
            outline: none;
            font-family: inherit;
            /* 关键修复：只使用flex属性 */
            flex: 1 1 0;
            min-width: 80px;
            max-width: calc(100% - 16px);
            margin-right: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
            border: 2px dashed rgba(0, 0, 255, 0.3);
        }
        
        /* 修复后的flex布局 - 显示状态 */
        .test-title-display-fixed {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.06);
            /* 关键修复：与输入框相同的flex属性 */
            flex: 1 1 0;
            min-width: 80px;
            max-width: calc(100% - 16px);
            margin-right: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
            border: 2px dashed rgba(0, 0, 255, 0.3);
        }
        
        /* 问题版本 - 显示状态 */
        .test-title-display-problem {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
            background: rgba(255, 0, 0, 0.1);
            /* 问题：width 100% 可能会影响容器 */
            width: 100%;
            flex: 1 1 auto;
            min-width: 80px;
            max-width: calc(100% - 16px);
            margin-right: 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
            border: 2px dashed rgba(255, 0, 0, 0.5);
        }
        
        .test-controls {
            background: rgba(0, 0, 0, 0.06);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            /* 重要：删除按钮的固定宽度 */
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px dashed rgba(255, 165, 0, 0.5);
        }
        
        .test-content {
            padding: 16px;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0 0 6px 6px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .width-control {
            margin: 10px 0;
        }
        
        .width-control input {
            width: 300px;
            margin: 0 10px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
        }
        
        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .legend {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .legend-box {
            width: 16px;
            height: 16px;
            border: 2px dashed;
        }
        
        .red { border-color: rgba(255, 0, 0, 0.5); }
        .green { border-color: rgba(0, 255, 0, 0.5); }
        .blue { border-color: rgba(0, 0, 255, 0.5); }
        .orange { border-color: rgba(255, 165, 0, 0.5); }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .problem {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
        }
        
        .fixed {
            background: #f0fdf4;
            border-left: 4px solid #22c55e;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Flex布局容器稳定性测试</h1>
        <p>测试标题编辑状态切换时，容器是否保持稳定的宽度</p>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-box red"></div>
                <span>容器边界</span>
            </div>
            <div class="legend-item">
                <div class="legend-box green"></div>
                <span>标题容器</span>
            </div>
            <div class="legend-item">
                <div class="legend-box blue"></div>
                <span>标题元素</span>
            </div>
            <div class="legend-item">
                <div class="legend-box orange"></div>
                <span>删除按钮</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>动态宽度测试</h3>
            <div class="width-control">
                <label>便签宽度: <span id="widthValue">300px</span></label>
                <br>
                <input type="range" id="widthSlider" min="200" max="600" value="300" step="10">
            </div>
            
            <div class="comparison">
                <div class="comparison-item problem">
                    <h4>❌ 问题版本 (width: 100%)</h4>
                    <div class="test-note" id="problemNote" style="width: 300px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display-problem" id="problemTitle">
                                    这是一个很长的便签标题，可能会影响容器布局的稳定性
                                </h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">使用 width: 100% 可能会导致容器扩展</div>
                    </div>
                </div>
                
                <div class="comparison-item fixed">
                    <h4>✅ 修复版本 (flex: 1 1 0)</h4>
                    <div class="test-note" id="fixedNote" style="width: 300px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display-fixed" id="fixedTitle">
                                    这是一个很长的便签标题，容器布局应该保持稳定
                                </h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">使用 flex: 1 1 0 确保容器稳定</div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center;">
                <button onclick="setVeryLongTitle()">设置超长标题</button>
                <button onclick="setMediumTitle()">设置中等标题</button>
                <button onclick="setShortTitle()">设置短标题</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>编辑状态模拟</h3>
            
            <div class="comparison">
                <div class="comparison-item">
                    <h4>编辑状态 (输入框)</h4>
                    <div class="test-note" style="width: 350px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <input 
                                    class="test-title-input-fixed" 
                                    value="编辑状态：输入框应该保持容器稳定"
                                    readonly
                                >
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">输入框使用 flex: 1 1 0</div>
                    </div>
                </div>
                
                <div class="comparison-item">
                    <h4>显示状态 (修复后)</h4>
                    <div class="test-note" style="width: 350px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display-fixed">
                                    显示状态：标题显示应该与输入框一致
                                </h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">标题显示也使用 flex: 1 1 0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="result">
            <h4>🎯 修复要点</h4>
            <ul>
                <li><strong>统一 flex 属性</strong>：编辑状态和显示状态都使用 <code>flex: 1 1 0</code></li>
                <li><strong>移除 width: 100%</strong>：避免元素尝试占据额外空间</li>
                <li><strong>保持 max-width 限制</strong>：防止超出容器边界</li>
                <li><strong>确保容器稳定性</strong>：文本长度变化不会影响容器布局</li>
                <li><strong>保持最小宽度</strong>：确保在窄容器中仍可用</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🧪 测试检查清单</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>编辑状态测试</h4>
                    <ul>
                        <li>□ 输入框不超出容器</li>
                        <li>□ 长文本在输入框内正确滚动</li>
                        <li>□ 容器宽度保持稳定</li>
                        <li>□ 删除按钮位置固定</li>
                    </ul>
                </div>
                <div>
                    <h4>显示状态测试</h4>
                    <ul>
                        <li>□ 标题不超出容器</li>
                        <li>□ 长文本显示省略号</li>
                        <li>□ 容器宽度与编辑状态一致</li>
                        <li>□ 删除按钮位置与编辑状态一致</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 动态宽度控制
        const widthSlider = document.getElementById('widthSlider');
        const widthValue = document.getElementById('widthValue');
        const problemNote = document.getElementById('problemNote');
        const fixedNote = document.getElementById('fixedNote');
        
        widthSlider.addEventListener('input', function() {
            const width = this.value + 'px';
            widthValue.textContent = width;
            problemNote.style.width = width;
            fixedNote.style.width = width;
        });
        
        function setVeryLongTitle() {
            const longTitle = "这是一个非常非常长的便签标题，专门用来测试当标题内容极其长时，不同布局方式对容器稳定性的影响，我们需要确保无论标题多长，容器的布局都保持稳定，不会被撑大或者产生其他布局问题";
            
            document.getElementById('problemTitle').textContent = longTitle;
            document.getElementById('fixedTitle').textContent = longTitle;
            
            // 更新所有相关元素
            document.querySelectorAll('.test-title-display-fixed').forEach(el => {
                el.textContent = longTitle;
            });
            document.querySelectorAll('.test-title-input-fixed').forEach(el => {
                el.value = longTitle;
            });
        }
        
        function setMediumTitle() {
            const mediumTitle = "中等长度的便签标题，用来测试正常情况下的布局表现";
            
            document.getElementById('problemTitle').textContent = mediumTitle;
            document.getElementById('fixedTitle').textContent = mediumTitle;
            
            // 更新所有相关元素
            document.querySelectorAll('.test-title-display-fixed').forEach(el => {
                el.textContent = mediumTitle;
            });
            document.querySelectorAll('.test-title-input-fixed').forEach(el => {
                el.value = mediumTitle;
            });
        }
        
        function setShortTitle() {
            const shortTitle = "短标题";
            
            document.getElementById('problemTitle').textContent = shortTitle;
            document.getElementById('fixedTitle').textContent = shortTitle;
            
            // 更新所有相关元素
            document.querySelectorAll('.test-title-display-fixed').forEach(el => {
                el.textContent = shortTitle;
            });
            document.querySelectorAll('.test-title-input-fixed').forEach(el => {
                el.value = shortTitle;
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Flex布局容器稳定性测试页面已加载');
        });
    </script>
</body>
</html>
