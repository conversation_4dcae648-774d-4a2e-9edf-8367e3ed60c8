<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>便签标题溢出修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .test-note {
            width: 250px;
            height: 200px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            margin: 10px;
            display: inline-block;
            vertical-align: top;
        }

        .test-note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #ffeaa7;
        }

        .test-title-container {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 0;
            overflow: hidden;
        }

        .test-title {
            flex: 1 1 0;
            min-width: 80px;
            max-width: calc(100% - 50px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 8px;
            box-sizing: border-box;
            padding: 2px 8px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.06);
            font-weight: 600;
            font-size: 14px;
            margin: 0;
        }

        .test-input {
            flex: 1 1 0;
            min-width: 80px;
            max-width: calc(100% - 50px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 8px;
            box-sizing: border-box;
            padding: 2px 8px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.06);
            font-weight: 600;
            font-size: 14px;
            border: none;
            outline: none;
        }

        .test-delete-btn {
            background: rgba(0, 0, 0, 0.06);
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            color: #666;
        }

        .test-delete-btn:hover {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
        }

        .narrow {
            width: 180px;
        }

        .wide {
            width: 350px;
        }

        .instruction {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔧 便签标题溢出修复测试</h1>

        <div class="instruction">
            <h3>测试说明</h3>
            <p>这个页面用于测试便签标题在不同宽度下的显示效果，验证修复是否成功：</p>
            <ul>
                <li>✅ 标题不应该超出便签边界</li>
                <li>✅ 删除按钮应该始终可见且位置固定</li>
                <li>✅ 超长标题应该显示省略号</li>
                <li>✅ 输入框和显示状态应该保持一致的布局</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>标准宽度便签 (250px)</h2>

            <div class="test-note">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <h3 class="test-title">这是一个非常非常长的便签标题，用来测试溢出效果</h3>
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>

            <div class="test-note">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <input class="test-input" value="这是一个非常非常长的便签标题，用来测试溢出效果" />
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>窄便签 (180px)</h2>

            <div class="test-note narrow">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <h3 class="test-title">超长标题测试</h3>
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>

            <div class="test-note narrow">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <input class="test-input" value="超长标题测试" />
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>宽便签 (350px)</h2>

            <div class="test-note wide">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <h3 class="test-title">这是一个非常非常长的便签标题，用来测试在宽便签中的显示效果</h3>
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>

            <div class="test-note wide">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <input class="test-input" value="这是一个非常非常长的便签标题，用来测试在宽便签中的显示效果" />
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>交互测试</h2>
            <p>点击下面的输入框，尝试输入更长的文本：</p>

            <div class="test-note">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <input class="test-input" placeholder="在这里输入长标题测试..." />
                    </div>
                    <button class="test-delete-btn">🗑️</button>
                </div>
            </div>
        </div>

        <div class="instruction">
            <h3>✅ 修复验证要点</h3>
            <ol>
                <li><strong>布局一致性</strong>：输入框和标题显示的宽度应该完全一致</li>
                <li><strong>删除按钮位置</strong>：删除按钮应该始终在右侧固定位置，不被挤出</li>
                <li><strong>文本溢出处理</strong>：超长文本应该显示省略号，而不是换行或溢出</li>
                <li><strong>响应式适应</strong>：在不同便签宽度下都应该正常工作</li>
                <li><strong>最小宽度保护</strong>：即使在很窄的便签中，标题区域也应该有最小可用宽度</li>
            </ol>
        </div>
    </div>

    <script>
        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function () {
            const inputs = document.querySelectorAll('.test-input');
            inputs.forEach(input => {
                input.addEventListener('input', function () {
                    console.log('输入长度:', this.value.length, '内容:', this.value);
                });
            });
        });
    </script>
</body>

</html>