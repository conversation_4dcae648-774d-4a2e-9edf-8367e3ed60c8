<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>便签标题编辑功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .test-note {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin: 20px 0;
            background: #fbbf24;
            font-size: 14px;
            position: relative;
        }

        .test-note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            flex-wrap: nowrap;
            gap: 8px;
            min-height: 40px;
            position: relative;
        }

        .test-title-container {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 0;
            overflow: hidden;
            max-width: 100%;
        }

        .test-title-input {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            background: rgba(0, 0, 0, 0.06);
            border: none;
            border-radius: 4px;
            padding: 2px 8px;
            outline: none;
            font-family: inherit;
            width: 100%;
            max-width: calc(100% - 16px);
            min-width: 80px;
            flex: 1 1 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
            margin-right: 8px;
        }

        .test-title-input:focus {
            background: rgba(0, 0, 0, 0.1);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }

        .test-title-display {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.06);
            min-width: 0;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
        }

        .test-controls {
            background: rgba(0, 0, 0, 0.06);
            border-radius: 4px;
            padding: 4px;
            font-size: 12px;
            color: #666;
        }

        .test-content {
            padding: 16px;
            min-height: 100px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0 0 6px 6px;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .width-control {
            margin: 10px 0;
        }

        .width-control input {
            width: 300px;
            margin: 0 10px;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            background: #d1fae5;
            border: 1px solid #10b981;
            border-radius: 6px;
            color: #065f46;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #2563eb;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🧪 便签标题编辑功能测试</h1>
        <p>测试便签标题编辑在不同宽度下的显示效果</p>

        <div class="test-section">
            <h3>动态宽度测试</h3>
            <div class="width-control">
                <label>便签宽度: <span id="widthValue">300px</span></label>
                <br>
                <input type="range" id="widthSlider" min="200" max="600" value="300" step="10">
            </div>

            <div class="test-note" id="dynamicNote" style="width: 300px;">
                <div class="test-note-header">
                    <div class="test-title-container">
                        <h3 class="test-title-display" id="titleDisplay" onclick="startEdit()">
                            这是一个很长的便签标题，用来测试在不同宽度下的显示效果
                        </h3>
                        <input class="test-title-input" id="titleInput" style="display: none;" onblur="stopEdit()"
                            onkeydown="handleKeydown(event)" value="这是一个很长的便签标题，用来测试在不同宽度下的显示效果">
                    </div>
                    <div class="test-controls">×</div>
                </div>
                <div class="test-content">
                    双击标题来编辑，测试不同宽度下的输入框表现
                </div>
            </div>

            <button onclick="startEdit()">开始编辑标题</button>
            <button onclick="setLongTitle()">设置超长标题</button>
            <button onclick="setShortTitle()">设置短标题</button>
        </div>

        <div class="test-section">
            <h3>测试用例</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">

                <!-- 窄宽度测试 -->
                <div class="test-note" style="width: 200px;">
                    <div class="test-note-header">
                        <div class="test-title-container">
                            <input class="test-title-input" value="窄便签标题测试" readonly>
                        </div>
                        <div class="test-controls">×</div>
                    </div>
                    <div class="test-content" style="min-height: 60px;">
                        窄宽度 (200px)
                    </div>
                </div>

                <!-- 中等宽度测试 -->
                <div class="test-note" style="width: 350px;">
                    <div class="test-note-header">
                        <div class="test-title-container">
                            <input class="test-title-input" value="中等宽度的便签标题测试内容" readonly>
                        </div>
                        <div class="test-controls">×</div>
                    </div>
                    <div class="test-content" style="min-height: 60px;">
                        中等宽度 (350px)
                    </div>
                </div>

                <!-- 宽便签测试 -->
                <div class="test-note" style="width: 500px;">
                    <div class="test-note-header">
                        <div class="test-title-container">
                            <input class="test-title-input" value="这是一个很宽的便签，可以容纳更长的标题内容进行测试" readonly>
                        </div>
                        <div class="test-controls">×</div>
                    </div>
                    <div class="test-content" style="min-height: 60px;">
                        宽便签 (500px)
                    </div>
                </div>

            </div>
        </div>

        <div class="result" id="testResult" style="display: none;">
            <h4>✅ 测试结果</h4>
            <p id="resultText">测试完成！</p>
        </div>

        <div class="test-section">
            <h3>🎯 优化要点</h3>
            <ul>
                <li><strong>响应式宽度</strong>：输入框能够根据容器宽度自动调整</li>
                <li><strong>最小宽度保护</strong>：确保输入框有足够的最小宽度（80px）</li>
                <li><strong>溢出处理</strong>：当内容超长时显示省略号</li>
                <li><strong>弹性布局</strong>：使用flex布局确保标题和控制按钮的合理分配</li>
                <li><strong>边距控制</strong>：合理的右边距确保不与删除按钮重叠</li>
            </ul>
        </div>
    </div>

    <script>
        let isEditing = false;

        // 动态宽度控制
        const widthSlider = document.getElementById('widthSlider');
        const widthValue = document.getElementById('widthValue');
        const dynamicNote = document.getElementById('dynamicNote');

        widthSlider.addEventListener('input', function () {
            const width = this.value + 'px';
            widthValue.textContent = width;
            dynamicNote.style.width = width;
        });

        // 标题编辑功能
        function startEdit() {
            if (isEditing) return;

            const titleDisplay = document.getElementById('titleDisplay');
            const titleInput = document.getElementById('titleInput');

            titleDisplay.style.display = 'none';
            titleInput.style.display = 'block';
            titleInput.focus();
            titleInput.select();

            isEditing = true;

            showResult('开始编辑模式', 'success');
        }

        function stopEdit() {
            if (!isEditing) return;

            const titleDisplay = document.getElementById('titleDisplay');
            const titleInput = document.getElementById('titleInput');

            titleDisplay.textContent = titleInput.value;
            titleDisplay.style.display = 'block';
            titleInput.style.display = 'none';

            isEditing = false;

            showResult('编辑完成，标题已更新: "' + titleInput.value + '"', 'success');
        }

        function handleKeydown(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                stopEdit();
            } else if (event.key === 'Escape') {
                // 恢复原始值
                document.getElementById('titleInput').value = document.getElementById('titleDisplay').textContent;
                stopEdit();
            }
        }

        function setLongTitle() {
            const longTitle = "这是一个非常非常长的便签标题，用来测试当标题内容超出容器宽度时的处理效果和用户体验优化";
            document.getElementById('titleInput').value = longTitle;
            document.getElementById('titleDisplay').textContent = longTitle;
            showResult('设置超长标题: "' + longTitle + '"', 'info');
        }

        function setShortTitle() {
            const shortTitle = "短标题";
            document.getElementById('titleInput').value = shortTitle;
            document.getElementById('titleDisplay').textContent = shortTitle;
            showResult('设置短标题: "' + shortTitle + '"', 'info');
        }

        function showResult(message, type) {
            const resultDiv = document.getElementById('testResult');
            const resultText = document.getElementById('resultText');

            resultText.textContent = message;
            resultDiv.style.display = 'block';

            if (type === 'success') {
                resultDiv.style.background = '#d1fae5';
                resultDiv.style.borderColor = '#10b981';
                resultDiv.style.color = '#065f46';
            } else if (type === 'info') {
                resultDiv.style.background = '#dbeafe';
                resultDiv.style.borderColor = '#3b82f6';
                resultDiv.style.color = '#1e40af';
            }

            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 3000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            showResult('便签标题编辑功能测试已加载，可以开始测试', 'info');
        });
    </script>
</body>

</html>