<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>便签标题编辑状态切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .test-note {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            margin: 20px 0;
            background: #fbbf24;
            font-size: 14px;
            position: relative;
        }

        .test-note-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            flex-wrap: nowrap;
            gap: 8px;
            min-height: 40px;
            position: relative;
        }

        .test-title-container {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 0;
            overflow: hidden;
            max-width: 100%;
        }

        /* 编辑状态的输入框样式 */
        .test-title-input {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            background: rgba(0, 0, 0, 0.06);
            border: none;
            border-radius: 4px;
            padding: 2px 8px;
            outline: none;
            font-family: inherit;
            width: 100%;
            max-width: calc(100% - 16px);
            min-width: 80px;
            flex: 1 1 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
            margin-right: 8px;
        }

        /* 非编辑状态的标题显示样式 */
        .test-title-display {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            cursor: pointer;
            padding: 2px 8px;
            border-radius: 4px;
            background: rgba(0, 0, 0, 0.06);
            /* 关键：与输入框保持一致的布局样式 */
            display: block;
            width: 100%;
            min-width: 80px;
            max-width: calc(100% - 16px);
            margin-right: 8px;
            flex: 1 1 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            box-sizing: border-box;
        }

        .test-title-input:focus {
            background: rgba(0, 0, 0, 0.1);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }

        .test-controls {
            background: rgba(0, 0, 0, 0.06);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            color: #666;
            cursor: pointer;
        }

        .test-content {
            padding: 16px;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0 0 6px 6px;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }

        .width-control {
            margin: 10px 0;
        }

        .width-control input {
            width: 300px;
            margin: 0 10px;
        }

        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .comparison-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
        }

        .comparison-item h4 {
            margin: 0 0 10px 0;
            color: #374151;
        }

        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .editing {
            background: #fef3c7;
            color: #92400e;
        }

        .viewing {
            background: #d1fae5;
            color: #065f46;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #2563eb;
        }

        button.toggle {
            background: #10b981;
        }

        button.toggle:hover {
            background: #059669;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            background: #dbeafe;
            border: 1px solid #3b82f6;
            border-radius: 6px;
            color: #1e40af;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔄 便签标题编辑状态切换测试</h1>
        <p>专门测试便签标题在编辑状态与显示状态之间切换时的布局一致性</p>

        <div class="test-section">
            <h3>动态状态切换测试</h3>
            <div class="width-control">
                <label>便签宽度: <span id="widthValue">300px</span></label>
                <br>
                <input type="range" id="widthSlider" min="200" max="600" value="300" step="10">
            </div>

            <div class="comparison">
                <div class="comparison-item">
                    <h4>编辑状态</h4>
                    <div class="status-indicator editing">EDITING</div>
                    <div class="test-note" id="editingNote" style="width: 300px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <input class="test-title-input" id="editingInput" value="这是一个很长的便签标题，用来测试编辑状态下的显示效果"
                                    readonly>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">编辑状态：输入框应该正确适应容器宽度</div>
                    </div>
                </div>

                <div class="comparison-item">
                    <h4>显示状态</h4>
                    <div class="status-indicator viewing">VIEWING</div>
                    <div class="test-note" id="viewingNote" style="width: 300px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display" id="viewingTitle">
                                    这是一个很长的便签标题，用来测试编辑状态下的显示效果
                                </h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content">显示状态：标题应该与编辑状态保持一致的布局</div>
                    </div>
                </div>
            </div>

            <div style="text-align: center;">
                <button class="toggle" onclick="toggleEditMode()">切换编辑/显示状态</button>
                <button onclick="setLongTitle()">设置超长标题</button>
                <button onclick="setShortTitle()">设置短标题</button>
            </div>
        </div>

        <div class="test-section">
            <h3>不同宽度下的一致性测试</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">

                <!-- 窄便签测试 -->
                <div>
                    <h4 style="text-align: center; margin: 0 0 10px 0;">窄便签 (220px)</h4>
                    <div class="test-note" style="width: 220px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display">超长标题测试文本内容</h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content" style="min-height: 40px;">显示状态</div>
                    </div>
                </div>

                <!-- 中等便签测试 -->
                <div>
                    <h4 style="text-align: center; margin: 0 0 10px 0;">中等便签 (350px)</h4>
                    <div class="test-note" style="width: 350px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display">超长标题测试文本内容展示效果</h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content" style="min-height: 40px;">显示状态</div>
                    </div>
                </div>

                <!-- 宽便签测试 -->
                <div>
                    <h4 style="text-align: center; margin: 0 0 10px 0;">宽便签 (500px)</h4>
                    <div class="test-note" style="width: 500px;">
                        <div class="test-note-header">
                            <div class="test-title-container">
                                <h3 class="test-title-display">超长标题测试文本内容展示效果在宽便签中的表现</h3>
                            </div>
                            <div class="test-controls">×</div>
                        </div>
                        <div class="test-content" style="min-height: 40px;">显示状态</div>
                    </div>
                </div>

            </div>
        </div>

        <div class="result" id="testResult">
            <h4>✅ 测试要点</h4>
            <ul>
                <li><strong>一致性</strong>：编辑状态和显示状态的宽度应该完全一致</li>
                <li><strong>不溢出</strong>：无论哪种状态，标题都不应该超出便签边界</li>
                <li><strong>省略号</strong>：超长标题应该显示省略号</li>
                <li><strong>最小宽度</strong>：即使在最窄的便签中，标题也应该有合理的最小宽度</li>
                <li><strong>间距</strong>：标题与删除按钮之间应该保持适当间距</li>
            </ul>
        </div>
    </div>

    <script>
        let isInEditMode = false;

        // 动态宽度控制
        const widthSlider = document.getElementById('widthSlider');
        const widthValue = document.getElementById('widthValue');
        const editingNote = document.getElementById('editingNote');
        const viewingNote = document.getElementById('viewingNote');

        widthSlider.addEventListener('input', function () {
            const width = this.value + 'px';
            widthValue.textContent = width;
            editingNote.style.width = width;
            viewingNote.style.width = width;
        });

        function toggleEditMode() {
            const editingContainer = document.querySelector('.comparison-item:first-child');
            const viewingContainer = document.querySelector('.comparison-item:last-child');

            if (isInEditMode) {
                // 切换到显示模式
                editingContainer.style.opacity = '0.5';
                viewingContainer.style.opacity = '1';
                editingContainer.querySelector('.status-indicator').textContent = 'INACTIVE';
                viewingContainer.querySelector('.status-indicator').textContent = 'ACTIVE';
                viewingContainer.querySelector('.status-indicator').className = 'status-indicator viewing';
                isInEditMode = false;
            } else {
                // 切换到编辑模式
                editingContainer.style.opacity = '1';
                viewingContainer.style.opacity = '0.5';
                editingContainer.querySelector('.status-indicator').textContent = 'ACTIVE';
                viewingContainer.querySelector('.status-indicator').textContent = 'INACTIVE';
                editingContainer.querySelector('.status-indicator').className = 'status-indicator editing';
                isInEditMode = true;
            }
        }

        function setLongTitle() {
            const longTitle = "这是一个非常非常长的便签标题，用来测试当标题内容超出容器宽度时两种状态下的处理效果和用户体验的一致性表现";
            document.getElementById('editingInput').value = longTitle;
            document.getElementById('viewingTitle').textContent = longTitle;

            // 更新所有测试便签的标题
            document.querySelectorAll('.test-title-display').forEach(el => {
                el.textContent = longTitle;
            });
        }

        function setShortTitle() {
            const shortTitle = "短标题";
            document.getElementById('editingInput').value = shortTitle;
            document.getElementById('viewingTitle').textContent = shortTitle;

            // 更新所有测试便签的标题
            document.querySelectorAll('.test-title-display').forEach(el => {
                el.textContent = shortTitle;
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 默认显示模式为激活状态
            toggleEditMode();
            toggleEditMode(); // 两次调用确保最终是显示模式
        });
    </script>
</body>

</html>