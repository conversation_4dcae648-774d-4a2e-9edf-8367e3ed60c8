/* 便签磨砂效果变量已移至全局 src/styles/glass-effects.css */

/* 🎯 完美滚动条布局 - 文本区域左右留白一致，滚动条在右侧留白内 */

/* 🎯 完美滚动条布局已实现 - 文本区域左右对称，滚动条在右侧留白内 */

/* 深色模式下的滚动条样式 */
@media (prefers-color-scheme: dark) {
  /* 深色模式下的工具栏按钮样式 */
  .toolbar-button {
    color: rgba(255, 255, 255, 0.7);
  }

  .toolbar-button.active {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
  }

  .toolbar-button:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
  }

  .toolbar-divider {
    background: rgba(255, 255, 255, 0.15);
    opacity: 0.5;
  }

  /* 深色模式下的表格按钮特殊处理 */
  .toolbar-button[title*="表格"],
  .toolbar-button[title*="添加列"],
  .toolbar-button[title*="添加行"] {
    color: rgba(255, 255, 255, 0.75);
  }

  .toolbar-button[title*="表格"].active,
  .toolbar-button[title*="添加列"]:hover:not(:disabled),
  .toolbar-button[title*="添加行"]:hover:not(:disabled) {
    color: rgba(255, 255, 255, 0.9);
  }
}

/* 简化的工具栏样式 - 直接使用toolbar-content */
.toolbar-content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 4px 16px;
  min-height: 32px;
  box-sizing: border-box;
  margin: 2px 0;
  gap: 6px;
}

/* 工具栏按钮组 */
.toolbar-button-group {
  display: flex;
  gap: 2px;
  align-items: center;
}

/* 工具栏按钮样式 - 小巧精致 */
.toolbar-button {
  width: 22px;
  height: 22px;
  border: none;
  border-radius: 3px;
  background: transparent;
  color: rgba(0, 0, 0, 0.5);
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1;
  user-select: none;
}

/* 统一所有工具栏按钮字体大小 */
.toolbar-button {
  font-size: 11px;
}

/* 表格按钮使用稍大字体以确保可读性 */
.toolbar-button[title*="表格"] {
  font-size: 12px;
}

/* 表格操作按钮保持标准字体大小 */
.toolbar-button[title*="添加列"],
.toolbar-button[title*="添加行"] {
  font-size: 11px;
  font-weight: 600;
}

/* 工具栏按钮激活状态 */
.toolbar-button.active {
  background: rgba(0, 0, 0, 0.08);
  color: rgba(0, 0, 0, 0.75);
}

/* 工具栏按钮悬停效果 */
.toolbar-button:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.06);
  color: rgba(0, 0, 0, 0.7);
}

/* 工具栏按钮禁用状态 */
.toolbar-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* 工具栏分隔线 */
.toolbar-divider {
  width: 1px;
  height: 14px;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0.6;
}

/* 已移除未使用的 .sticky-note-format-toolbar.inside 样式 */

.sticky-note {
  position: absolute;
  border: none; /* 去掉边线 */
  border-radius: 8px;

  /* 半透明磨砂效果 - 使用CSS变量 */
  background: var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur);
  -webkit-backdrop-filter: var(--note-glass-blur);
  border: 1px solid var(--note-glass-border);
  box-shadow: var(--note-glass-shadow);

  user-select: none;
  /* 默认最小尺寸 - 适用于100%及以上缩放 */
  min-width: 250px;
  min-height: 230px;
  display: flex;
  flex-direction: column;
  font-size: var(--note-content-font-size, 14px); /* 使用CSS变量控制字体大小 */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
    "SF Pro Text", "Segoe UI", "Helvetica Neue", "Roboto", "Inter", "Arial",
    sans-serif; /* 跨平台优质字体栈 */

  /* 字体渲染优化 - 保持基本的清晰度设置 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* 性能优化 */
  will-change: auto;
  backface-visibility: hidden;

  /* 平滑过渡效果 - 只对视觉效果应用过渡，不影响拖拽性能 */
  transition: backdrop-filter 0.2s ease, box-shadow 0.2s ease;
}

/* 浏览器降级方案 - 不支持 backdrop-filter 的情况 */
@supports not (backdrop-filter: blur(8px)) {
  .sticky-note {
    background: var(--note-glass-fallback);
  }
}

/* 表情符号特殊处理 - 确保在所有缩放级别下正确显示 */
.sticky-note :is(span, p, div, h1, h2, h3, h4, h5, h6, li, td, th) {
  /* 使用CSS变量控制表情符号字体大小 */
  font-variant-emoji: normal;
  /* 确保表情符号使用系统默认渲染 */
  text-rendering: auto;
}

/* 针对表情符号的字体大小优化 - 使用更广泛的选择器 */
.sticky-note .sticky-note-title,
.sticky-note .sticky-note-title-input {
  /* 为包含表情符号的文本提供更好的渲染 */
  font-variant-emoji: normal;
  text-rendering: optimizeLegibility;
}

/* 通过伪元素和CSS变量优化表情符号显示 */
.sticky-note {
  /* 设置表情符号的基础样式变量 */
  --emoji-line-height: 1.2;
  --emoji-vertical-align: baseline;
}

/* 移动模式下便签的样式 */
.sticky-note.move-mode-disabled {
  cursor: grab !important;
  /* 移除透明度和灰度滤镜，保持便签正常外观 */
}

.sticky-note.move-mode-disabled .sticky-note-title,
.sticky-note.move-mode-disabled .sticky-note-content {
  pointer-events: none;
}

.sticky-note.move-mode-disabled::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 10;
  cursor: grab;
}

/* 流式便签样式 - 使用box-shadow统一边框效果，避免与选中状态叠加 */
.sticky-note.streaming {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果，与选中状态一致 */ 0 0 10px rgba(24, 144, 255, 0.3),
    /* 外发光效果 */ 0 2px 8px rgba(0, 0, 0, 0.1),
    /* 保持原有阴影 */ 0 4px 20px rgba(0, 0, 0, 0.06); /* 保持原有阴影 */
  animation: streamingPulse 2s ease-in-out infinite;
}

@keyframes streamingPulse {
  0%,
  100% {
    box-shadow: inset 0 0 0 2px #1890ff,
      /* 内边框效果 */ 0 0 10px rgba(24, 144, 255, 0.3),
      /* 外发光效果 */ 0 2px 8px rgba(0, 0, 0, 0.1),
      /* 保持原有阴影 */ 0 4px 20px rgba(0, 0, 0, 0.06); /* 保持原有阴影 */
  }
  50% {
    box-shadow: inset 0 0 0 2px #1890ff,
      /* 内边框效果 */ 0 0 15px rgba(24, 144, 255, 0.5),
      /* 增强外发光效果 */ 0 2px 8px rgba(0, 0, 0, 0.1),
      /* 保持原有阴影 */ 0 4px 20px rgba(0, 0, 0, 0.06); /* 保持原有阴影 */
  }
}

/* 选中状态样式 - 使用box-shadow模拟边框，完全避免像素偏移 */
.sticky-note.selected {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果 */ 0 0 0 2px rgba(24, 144, 255, 0.2),
    /* 外发光效果 */ 0 2px 8px rgba(0, 0, 0, 0.1),
    /* 原有阴影 */ 0 4px 20px rgba(0, 0, 0, 0.06); /* 原有阴影 */
}

/* 选中状态优先级高于流式状态 */
.sticky-note.selected.streaming {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果 */ 0 0 0 2px rgba(24, 144, 255, 0.3),
    /* 外发光效果 */ 0 0 15px rgba(24, 144, 255, 0.4); /* 流式发光效果 */
  animation: selectedStreamingPulse 2s ease-in-out infinite;
}

/* 选中+流式状态下的悬浮效果 */
.sticky-note.selected.streaming:hover {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果 */ 0 0 0 3px rgba(24, 144, 255, 0.4),
    /* 增强外发光效果 */ 0 0 20px rgba(24, 144, 255, 0.5),
    /* 悬浮阴影 */ 0 4px 16px rgba(0, 0, 0, 0.12),
    0 8px 32px rgba(0, 0, 0, 0.08);
  animation: selectedStreamingPulseHover 2s ease-in-out infinite;
}

@keyframes selectedStreamingPulse {
  0%,
  100% {
    box-shadow: inset 0 0 0 2px #1890ff, 0 0 0 2px rgba(24, 144, 255, 0.3),
      0 0 15px rgba(24, 144, 255, 0.4);
  }
  50% {
    box-shadow: inset 0 0 0 2px #1890ff, 0 0 0 2px rgba(24, 144, 255, 0.5),
      0 0 20px rgba(24, 144, 255, 0.6);
  }
}

/* 选中+流式状态悬浮时的动画 */
@keyframes selectedStreamingPulseHover {
  0%,
  100% {
    box-shadow: inset 0 0 0 2px #1890ff, 0 0 0 3px rgba(24, 144, 255, 0.4),
      0 0 20px rgba(24, 144, 255, 0.5), 0 4px 16px rgba(0, 0, 0, 0.12),
      0 8px 32px rgba(0, 0, 0, 0.08);
  }
  50% {
    box-shadow: inset 0 0 0 2px #1890ff, 0 0 0 4px rgba(24, 144, 255, 0.6),
      0 0 25px rgba(24, 144, 255, 0.7), 0 4px 16px rgba(0, 0, 0, 0.12),
      0 8px 32px rgba(0, 0, 0, 0.08);
  }
}

/* 流式内容容器 */
.streaming-content {
  position: relative;
  display: inline-block;
  width: 100%;
  user-select: text; /* 允许选择流式内容中的文本 */
}

/* 流式内容中的分割线样式 */
.streaming-content hr {
  border: none;
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  margin: 16px 0;
}

/* 流式光标样式 */
.streaming-cursor {
  display: inline-block;
  color: #1890ff;
  font-weight: bold;
  animation: cursorBlink 1s infinite;
  margin-left: 2px;
}

@keyframes cursorBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 便签颜色变体 - 半透明磨砂效果叠加 */
.sticky-note.color-yellow {
  background: linear-gradient(
      rgba(255, 250, 214, 0.4),
      rgba(255, 250, 214, 0.4)
    ),
    var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur) hue-rotate(5deg);
  -webkit-backdrop-filter: var(--note-glass-blur) hue-rotate(5deg);
}

.sticky-note.color-blue {
  background: linear-gradient(
      rgba(225, 243, 253, 0.4),
      rgba(225, 243, 253, 0.4)
    ),
    var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur) hue-rotate(-10deg);
  -webkit-backdrop-filter: var(--note-glass-blur) hue-rotate(-10deg);
}

.sticky-note.color-green {
  background: linear-gradient(
      rgba(229, 249, 240, 0.4),
      rgba(229, 249, 240, 0.4)
    ),
    var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur) hue-rotate(15deg);
  -webkit-backdrop-filter: var(--note-glass-blur) hue-rotate(15deg);
}

.sticky-note.color-pink {
  background: linear-gradient(
      rgba(254, 241, 240, 0.4),
      rgba(254, 241, 240, 0.4)
    ),
    var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur) hue-rotate(-5deg);
  -webkit-backdrop-filter: var(--note-glass-blur) hue-rotate(-5deg);
}

.sticky-note.color-purple {
  background: linear-gradient(
      rgba(242, 232, 247, 0.4),
      rgba(242, 232, 247, 0.4)
    ),
    var(--note-glass-bg);
  backdrop-filter: var(--note-glass-blur) hue-rotate(20deg);
  -webkit-backdrop-filter: var(--note-glass-blur) hue-rotate(20deg);
}

/* 颜色变体的降级方案 */
@supports not (backdrop-filter: blur(8px)) {
  .sticky-note.color-yellow {
    background: linear-gradient(
        rgba(255, 250, 214, 0.6),
        rgba(255, 250, 214, 0.6)
      ),
      var(--note-glass-fallback);
  }

  .sticky-note.color-blue {
    background: linear-gradient(
        rgba(225, 243, 253, 0.6),
        rgba(225, 243, 253, 0.6)
      ),
      var(--note-glass-fallback);
  }

  .sticky-note.color-green {
    background: linear-gradient(
        rgba(229, 249, 240, 0.6),
        rgba(229, 249, 240, 0.6)
      ),
      var(--note-glass-fallback);
  }

  .sticky-note.color-pink {
    background: linear-gradient(
        rgba(254, 241, 240, 0.6),
        rgba(254, 241, 240, 0.6)
      ),
      var(--note-glass-fallback);
  }

  .sticky-note.color-purple {
    background: linear-gradient(
        rgba(242, 232, 247, 0.6),
        rgba(242, 232, 247, 0.6)
      ),
      var(--note-glass-fallback);
  }
}

/* 悬浮效果 - 增强磨砂效果和边界感 */
.sticky-note:hover:not(.selected) {
  backdrop-filter: blur(12px) saturate(180%);
  -webkit-backdrop-filter: blur(12px) saturate(180%);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15),
    /* 近距离锐利阴影 */ 0 6px 20px rgba(0, 0, 0, 0.1),
    /* 中距离阴影 */ 0 12px 40px rgba(0, 0, 0, 0.06); /* 远距离柔和阴影 */
  transform: translateY(-2px); /* 增强上浮效果 */
  transition: all 0.2s ease;
}

/* 选中状态和编辑状态的悬浮效果 - 统一样式 */
.sticky-note.selected:hover,
.sticky-note.editing:hover {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果 */ 0 0 0 3px rgba(24, 144, 255, 0.3),
    /* 增强外发光效果 */ 0 4px 16px rgba(0, 0, 0, 0.12),
    /* 悬浮阴影 */ 0 8px 32px rgba(0, 0, 0, 0.08); /* 悬浮阴影 */
}

.sticky-note.dragging {
  cursor: move;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2),
    /* 近距离强阴影，突出拖拽状态 */ 0 8px 24px rgba(0, 0, 0, 0.15),
    /* 中距离阴影 */ 0 16px 48px rgba(0, 0, 0, 0.08); /* 远距离柔和阴影 */
  /* 拖拽时禁用所有过渡效果，确保流畅性能 */
  transition: none !important;
}

/* 缩放状态 - 禁用过渡效果避免顿挫 */
.sticky-note.resizing {
  transition: none !important;
}

/* 编辑状态样式 - 统一使用选中状态的样式 */
.sticky-note.editing {
  box-shadow: inset 0 0 0 2px #1890ff,
    /* 内边框效果 */ 0 0 0 2px rgba(24, 144, 255, 0.2),
    /* 外发光效果 */ 0 2px 8px rgba(0, 0, 0, 0.1),
    /* 原有阴影 */ 0 4px 20px rgba(0, 0, 0, 0.06); /* 原有阴影 */
}

.sticky-note.new {
  animation: fadeInMinimal 0.2s ease-out;
}

@keyframes fadeInMinimal {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.sticky-note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  /* 移除独立背景色 */
  /* background: rgba(0, 0, 0, 0.02); */
  /* 移除分割线 */
  /* border-bottom: 1px solid rgba(0, 0, 0, 0.08); */
  border-radius: 8px 8px 0 0;
  /* 更新flex布局 */
  flex-wrap: nowrap;
  gap: 8px; /* 标题区域和按钮区域之间的间距 */

  /* 确保在所有缩放级别下都能正确布局 */
  min-height: 40px; /* 确保有足够的高度 */
  position: relative; /* 为子元素提供定位上下文 */
}

.sticky-note-title {
  margin: 0;
  font-size: var(
    --note-title-font-size,
    14px
  ); /* 使用CSS变量控制标题字体大小 */
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  padding: 2px 8px; /* 与输入框保持一致的内边距 */
  border-radius: 4px;
  /* 移除背景色过渡动画 */
  /* transition: background-color 0.2s ease; */
  /* 使用flex布局，自适应内容宽度 */
  flex: 0 1 auto;
  min-width: 20px; /* 最小宽度保证可用性 */
  width: auto; /* 自动宽度，根据内容调整 */
  /* max-width 现在通过JavaScript动态计算 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px; /* 与输入框保持一致的右边距 */
  box-sizing: border-box; /* 包含padding */
}

/* 标题悬停效果已移除 */

/* 标题宽度现在通过JavaScript动态计算，移除了静态CSS规则 */

.sticky-note-controls {
  display: flex;
  gap: 4px;
  justify-content: flex-end; /* 让按钮靠右对齐，删除按钮保持在右侧 */
  opacity: 0; /* 恢复透明度控制，默认隐藏 */
  /* 移除透明度过渡动画，让控件显示更直接 */
  /* transition: opacity 0.2s ease; */
  flex-shrink: 0; /* 防止按钮被压缩 */
  /* 移除固定最小宽度，让控件区域自适应按钮数量 */

  /* 确保在所有缩放级别下都能正确定位 */
  position: relative;
  min-width: fit-content; /* 确保有足够的空间容纳按钮 */
  max-width: 60px; /* 限制控件区域最大宽度，确保标题有足够空间 */
  align-items: center; /* 垂直居中对齐 */

  /* 增强稳定性 - 确保控件始终在正确位置 */
  width: auto; /* 自动宽度 */
  height: auto; /* 自动高度 */
  box-sizing: border-box; /* 包含padding和border */
  z-index: 10; /* 确保控件在其他元素之上 */
}

/* 恢复鼠标悬浮才显示控件的规则 */
.sticky-note:hover .sticky-note-controls {
  opacity: 1;
}

/* 编辑状态下始终显示控件 */
.sticky-note.editing .sticky-note-controls {
  opacity: 1;
}

.sticky-note-controls button {
  background: rgba(0, 0, 0, 0.06); /* 与标题背景色一致 */
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  /* 移除按钮背景色过渡动画 */
  /* transition: background-color 0.2s ease; */
}

.sticky-note-controls button:hover {
  background: rgba(0, 0, 0, 0.1); /* 悬浮时稍微深一点 */
}

.sticky-note-controls button.delete-button:hover {
  color: #ff4d4f !important; /* 悬浮时图标变为红色 */
  background: rgba(255, 77, 79, 0.1); /* 悬浮时背景带有淡红色 */
  transform: scale(1.05); /* 悬浮时轻微放大 */
}

/* 溯源按钮样式 - 与删除按钮保持一致 */
.sticky-note-controls button.source-button {
  background: rgba(0, 0, 0, 0.06); /* 与删除按钮背景色一致 */
  border: none;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease; /* 添加过渡动画 */
}

.sticky-note-controls button.source-button:hover {
  color: #fa8c16 !important; /* 悬浮时图标变为橙色 */
  background: rgba(250, 140, 22, 0.1); /* 悬浮时背景带有淡橙色 */
  transform: scale(1.05); /* 悬浮时轻微放大 */
}

/* 溯源按钮激活状态 */
.sticky-note-controls button.source-button.active {
  color: #fa8c16 !important; /* 激活时橙色 */
  background: rgba(250, 140, 22, 0.15); /* 激活时背景更明显 */
}

/* 针对小缩放级别的控件优化 - 使用数据属性来检测缩放级别 */
/* 25%缩放的控件样式在下面统一处理 */

.sticky-note[data-scale="0.5"] .sticky-note-controls,
.sticky-note[data-scale="0.75"] .sticky-note-controls {
  /* 50%和75%缩放时的定位策略 */
  position: absolute !important;
  right: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 50 !important;
  display: flex !important;
  gap: 2px !important;
  flex-shrink: 0 !important;
  align-items: center !important;
}

/* 25%缩放的头部样式在下面统一处理 */

.sticky-note[data-scale="0.5"] .sticky-note-header,
.sticky-note[data-scale="0.75"] .sticky-note-header {
  /* 50%和75%缩放时的处理 */
  position: relative !important;
  padding-right: 36px !important; /* 为控件留出空间 */
}

/* 在小缩放级别下调整拖拽区域 */
.sticky-note[data-scale="0.5"] .drag-handle,
.sticky-note[data-scale="0.75"] .drag-handle {
  /* 确保拖拽区域不会覆盖控件 */
  padding-right: 0 !important; /* 移除额外的padding，因为header已经有了 */
}

/* 在小缩放级别下调整按钮大小 */
.sticky-note[data-scale="0.25"] .sticky-note-controls button {
  /* 25%缩放时的极小按钮 */
  padding: 1px !important;
  font-size: 10px !important; /* 更小的图标 */
  min-width: 16px !important; /* 更小但仍可点击 */
  min-height: 16px !important;
  border-radius: 2px !important;
  line-height: 1 !important; /* 确保图标居中 */
}

.sticky-note[data-scale="0.5"] .sticky-note-controls button,
.sticky-note[data-scale="0.75"] .sticky-note-controls button {
  padding: 2px !important;
  font-size: 12px !important;
  min-width: 20px !important;
  min-height: 20px !important;
  border-radius: 3px !important;
}

/* 不同缩放级别的最小尺寸适配 */
/* 25%缩放时的完整解决方案 */
.sticky-note[data-scale="0.25"] {
  /* 确保便签本身有足够的空间容纳控件 */
  min-width: 50px !important; /* 最小宽度确保控件有空间 */
  min-height: 38px !important; /* 最小高度确保控件不会溢出 */
}

/* 50%缩放时的最小尺寸 - 关键修复 */
.sticky-note[data-scale="0.5"] {
  /* 50%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 125px !important; /* 250px * 0.5 = 125px */
  min-height: 115px !important; /* 230px * 0.5 = 115px */
}

/* 75%缩放时的最小尺寸 */
.sticky-note[data-scale="0.75"] {
  /* 75%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 188px !important; /* 250px * 0.75 = 187.5px，向上取整 */
  min-height: 173px !important; /* 230px * 0.75 = 172.5px，向上取整 */
}

/* 125%缩放时的最小尺寸 */
.sticky-note[data-scale="1.25"] {
  /* 125%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 313px !important; /* 250px * 1.25 = 312.5px，向上取整 */
  min-height: 288px !important; /* 230px * 1.25 = 287.5px，向上取整 */
}

/* 150%缩放时的最小尺寸 */
.sticky-note[data-scale="1.5"] {
  /* 150%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 375px !important; /* 250px * 1.5 = 375px */
  min-height: 345px !important; /* 230px * 1.5 = 345px */
}

/* 175%缩放时的最小尺寸 */
.sticky-note[data-scale="1.75"] {
  /* 175%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 438px !important; /* 250px * 1.75 = 437.5px，向上取整 */
  min-height: 403px !important; /* 230px * 1.75 = 402.5px，向上取整 */
}

/* 200%缩放时的最小尺寸 */
.sticky-note[data-scale="2"] {
  /* 200%缩放时，最小尺寸也应该按比例缩放 */
  min-width: 500px !important; /* 250px * 2 = 500px */
  min-height: 460px !important; /* 230px * 2 = 460px */
}

.sticky-note[data-scale="0.25"] .sticky-note-header {
  /* 完全重写头部布局 */
  position: relative !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important; /* 顶部对齐 */
  padding: 6px 8px !important;
  min-height: 24px !important;
  overflow: visible !important; /* 确保控件可见 */
}

.sticky-note[data-scale="0.25"] .drag-handle {
  /* 拖拽区域占据剩余空间 */
  flex: 1 !important;
  min-width: 0 !important;
  padding-right: 4px !important;
}

.sticky-note[data-scale="0.25"] .sticky-note-controls {
  /* 控件固定在右侧 */
  position: static !important; /* 使用静态定位，依赖flex布局 */
  flex-shrink: 0 !important;
  align-self: flex-start !important; /* 顶部对齐 */
  margin-top: 0 !important;
  z-index: 10 !important;
}

/* 便签工具栏样式 - 位于便签右侧，极简设计 */
.settings-toolbar {
  position: absolute;
  display: flex;
  gap: 4px; /* 进一步减小按钮间距 */
  background: rgba(255, 255, 255, 0.92);
  border-radius: 6px; /* 进一步减小圆角 */
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04); /* 进一步减轻阴影 */
  backdrop-filter: blur(6px); /* 进一步减少模糊效果 */
  border: 1px solid rgba(0, 0, 0, 0.04); /* 进一步减淡边框 */
  padding: 4px; /* 进一步减小内边距 */
  /* 移除固定的z-index，让JavaScript动态设置 */
  animation: settingsToolbarSlideIn 0.12s ease-out; /* 进一步加快动画速度 */
}

/* 竖排工具栏样式 - 垂直排列按钮 */
.settings-toolbar.vertical {
  flex-direction: column; /* 改为垂直排列 */
  animation: settingsToolbarSlideInVertical 0.12s ease-out; /* 使用垂直滑入动画 */
}

/* 设置工具栏滑入动画 - 水平版本 */
@keyframes settingsToolbarSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 设置工具栏滑入动画 - 垂直版本 */
@keyframes settingsToolbarSlideInVertical {
  from {
    opacity: 0;
    transform: translateX(10px) scale(0.95); /* 从右侧滑入 */
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 设置工具栏按钮 - 基于Ant Design Button的极简设计 */
.settings-toolbar .settings-toolbar-button {
  position: relative;
  width: 28px !important; /* 进一步减小尺寸 */
  height: 28px !important;
  min-width: 28px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: transparent !important;
  border-radius: 4px !important; /* 进一步减小圆角 */
  border: none !important;
  box-shadow: none !important;
  transition: all 0.12s ease !important;
  pointer-events: auto !important; /* 确保可以点击 */
  cursor: pointer !important; /* 确保显示指针 */
}

/* 增强选择器优先级，确保悬浮效果能覆盖Ant Design默认样式 */
.settings-toolbar .settings-toolbar-button.ant-btn:hover:not(.disabled),
.settings-toolbar .settings-toolbar-button:hover:not(.disabled) {
  background: rgba(22, 119, 255, 0.1) !important; /* 增强蓝色背景 */
  color: #1677ff !important; /* 图标变为主题蓝色 */
  transform: scale(1.05) !important; /* 轻微放大 */
  box-shadow: 0 2px 6px rgba(22, 119, 255, 0.2) !important; /* 添加蓝色阴影 */
  border-color: transparent !important; /* 确保边框透明 */
}

/* 确保图标也变成蓝色 */
.settings-toolbar .settings-toolbar-button:hover:not(.disabled) .anticon {
  color: #1677ff !important;
}

/* 激活状态样式 - 增强选择器优先级 */
.settings-toolbar .settings-toolbar-button.ant-btn.active,
.settings-toolbar .settings-toolbar-button.active {
  background: rgba(250, 140, 22, 0.1) !important;
  color: #fa8c16 !important;
}

.settings-toolbar .settings-toolbar-button.ant-btn.active:hover,
.settings-toolbar .settings-toolbar-button.active:hover {
  background: rgba(250, 140, 22, 0.14) !important;
  transform: scale(1.03) !important;
}

/* 禁用状态样式 - 增强选择器优先级 */
.settings-toolbar .settings-toolbar-button.ant-btn.disabled,
.settings-toolbar .settings-toolbar-button.disabled {
  opacity: 0.25 !important;
  cursor: not-allowed !important;
}

.settings-toolbar .settings-toolbar-button.ant-btn.disabled:hover,
.settings-toolbar .settings-toolbar-button.disabled:hover {
  transform: none !important;
  background: transparent !important;
  color: inherit !important;
}

/* 工具栏按钮图标样式 */
.settings-toolbar .settings-toolbar-button .anticon {
  font-size: 12px !important; /* 进一步减小图标尺寸 */
  color: inherit;
}

/* 工具栏徽章 - 右上角小数字，极简设计 */
.settings-toolbar .toolbar-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #fa8c16;
  color: white;
  font-size: var(--note-badge-font-size, 8px); /* 使用CSS变量控制徽章字体大小 */
  font-weight: bold;
  padding: 1px 2px; /* 进一步减小内边距 */
  border-radius: 4px; /* 进一步减小圆角 */
  min-width: 12px; /* 进一步减小最小宽度 */
  height: 12px; /* 进一步减小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid white; /* 进一步减细边框 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* 进一步减轻阴影 */
  z-index: 1;
}

.settings-toolbar .settings-toolbar-button.active .toolbar-badge {
  background: #d46b08;
}

.settings-toolbar .settings-toolbar-button.disabled .toolbar-badge {
  background: #ccc;
  opacity: 0.6;
}

.sticky-note-content {
  flex: 1;
  padding-left: 16px; /* 🎯 原生方案：左侧16px内边距 */
  padding-right: 16px; /* 动态右侧内边距：默认16px，有滚动条时自动调整为0 */
  /* padding-top: 16px; */
  padding-bottom: 16px; /* 非编辑状态的下内边距 */
  overflow: hidden; /* 修复：不允许内容溢出，确保高度约束生效 */
  display: flex;
  flex-direction: column;
  user-select: text; /* 允许选择内容区域的文本 */
  min-height: 0; /* 关键：允许flex子元素收缩 */
}

/* 当便签内容需要滚动时，去掉右侧内边距为滚动条让出空间 */
.sticky-note-content:has(.ProseMirror[data-scrollable="true"]) {
  padding-right: 0;
}

/* 备用方案：兼容不支持:has()的浏览器 */
.sticky-note-content.has-scrollbar {
  padding-right: 0;
}

/* 包含思维链的便签内容区域特殊处理 - 更新以适应新的结构层级 */
.sticky-note:has(.thinking-chain-in-note) .sticky-note-content {
  overflow: hidden; /* 保持正常的隐藏溢出，因为思维链现在在外层 */
}

/* 如果浏览器不支持:has选择器，使用类名方式 */
.sticky-note-content.has-thinking-chain {
  overflow: hidden; /* 保持正常的隐藏溢出，因为思维链现在在外层 */
}

/* 编辑状态下的sticky-note-content - 移除下内边距 */
.sticky-note.editing .sticky-note-content {
  padding-bottom: 0; /* 编辑状态时移除下内边距，让工具栏紧贴内容 */
}

/* 基本的文本渲染优化 - 简化版本 */
.sticky-note-title,
.sticky-note-title-input {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

.empty-note {
  color: #374151;
  font-style: normal;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  /* 保持透明，但功能可用 */
  background: transparent;
  opacity: 1; /* 始终可用，但透明 */
}

/* 缩放手柄悬停效果已移除 */

/* 优雅的弧形缩放提示符号 - 位于便签外部右下角，保持3px间距 */
.resize-indicator {
  position: absolute;
  bottom: -11px; /* 8px弧线高度 + 3px间距 */
  right: -11px; /* 8px弧线宽度 + 3px间距 */
  width: 11px;
  height: 11px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none; /* 不阻挡resize-handle的点击事件 */
  z-index: 1; /* 确保在resize-handle之上显示 */
}

/* 便签悬浮时显示缩放提示符号 */
.sticky-note:hover:not(.editing):not(.dragging) .resize-indicator {
  opacity: 0.4;
}

/* 完整的弧形指示器 - 同时实现弧形连接和圆头线段，8px粗线条 */
.resize-indicator::before {
  content: "";
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: transparent;
  border-bottom: 5px solid rgba(0, 0, 0, 0.3); /* 更粗的线条 */
  border-right: 5px solid rgba(0, 0, 0, 0.3); /* 更粗的线条 */
  border-radius: 2px 2px 13px 2px; /* 调整端点圆角以匹配线条宽度 */
  /* 4px: 右侧线段上端圆头（线条宽度的一半） */
  /* 4px: 保持对称 */
  /* 10px: 弧形连接处 */
  /* 4px: 底部线段左端圆头（线条宽度的一半） */
}

/* 响应式设计 - 在小缩放级别下调整字体 */
@media (max-width: 768px) {
  .sticky-note {
    font-size: 12px;
  }
}

.sticky-note-title-input {
  margin: 0;
  font-size: var(
    --note-title-font-size,
    14px
  ); /* 使用CSS变量控制标题输入框字体大小 */
  font-weight: 600;
  color: #374151;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 2px 8px; /* 与标题显示保持一致的内边距 */
  outline: none;
  font-family: inherit;
  /* 使用flex布局，自适应内容宽度，与标题显示保持一致 */
  flex: 0 1 auto;
  min-width: 20px; /* 最小宽度保证可用性 */
  width: auto; /* 自动宽度，根据内容调整 */
  /* max-width 现在通过JavaScript动态计算 */
  overflow: hidden; /* 防止内容溢出 */
  text-overflow: ellipsis; /* 超长文本显示省略号 */
  white-space: nowrap; /* 单行显示 */
  margin-right: 8px; /* 与标题显示保持一致的右边距 */
  box-sizing: border-box; /* 包含padding和border */
}

.sticky-note-title-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* AI生成加载指示器 - 简化版本 */
.ai-loading-indicator {
  position: absolute;
  bottom: 12px;
  left: 16px;
  display: flex;
  align-items: center;
  color: #666;
  font-size: calc(
    var(--note-content-font-size, 14px) * 0.85
  ); /* 使用相对于内容字体的大小 */
  opacity: 0.8;
}

.ai-loading-indicator .anticon {
  margin-right: 4px;
}

/* 连接点样式 - 位于便签左下角 */
.connection-point {
  position: absolute;
  bottom: 8px; /* 调整到便签内部，距离底部8px */
  left: 8px; /* 调整到便签内部，距离左边8px */
  width: 18px; /* 与插槽尺寸统一 */
  height: 18px; /* 与插槽尺寸统一 */
  cursor: pointer;
  z-index: 10;
  opacity: 0; /* 默认隐藏 */
  transition: all 0.2s ease; /* 添加所有属性的过渡动画 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 便签悬停时显示连接点 */
.sticky-note:hover .connection-point {
  opacity: 1;
}

/* 连接点内部的圆点 */
.connection-dot {
  width: 15px; /* 与插槽内部元素尺寸协调 */
  height: 15px;
  border-radius: 50%;
  background: rgba(22, 119, 255, 0.9); /* 增加不透明度 */
  border: 1px solid rgba(255, 255, 255, 1); /* 与插槽边框粗细一致 */
  transition: background-color 0.2s ease, box-shadow 0.2s ease; /* 只对颜色和阴影做过渡 */
  box-shadow: 0 1px 4px rgba(22, 119, 255, 0.3); /* 与插槽阴影一致 */
  /* 确保圆点完全居中，但不使用transform避免位置偏移 */
  margin: auto;
}

/* 连接点悬停效果 */
.connection-point:hover {
  transform: scale(1.1); /* 悬浮时轻微放大 */
}

.connection-point:hover .connection-dot {
  background: #1677ff; /* 主题蓝色 */
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.5); /* 增强蓝色阴影 */
  border-color: rgba(22, 119, 255, 0.3); /* 蓝色边框 */
}

/* 已连接状态的连接点 - 始终显示 */
.connection-point.connected {
  opacity: 1; /* 已连接时始终显示 */
}

/* 源便签连接点样式已优化 */

/* 正在被溯源连接线连接时的连接点 - 始终显示 */
.connection-point.being-source-connected {
  opacity: 1; /* 正在被溯源连接线连接时始终显示 */
}

/* 有溯源连接线激活时的连接点 - 始终显示 */
.connection-point.source-active {
  opacity: 1; /* 有溯源连接线时始终显示 */
}

/* 有溯源标识的连接点 - 如果已连接或有溯源连接线激活，则始终显示 */
.connection-point.has-source.connected,
.connection-point.has-source.source-active {
  opacity: 1; /* 已连接或有溯源连接线时始终显示 */
}

/* 便签悬停时显示所有连接点 - 统一的显示逻辑 */
.sticky-note:hover .connection-point {
  opacity: 1; /* 悬停时完全显示，包括有溯源标识的连接点 */
}

.connection-point.connected .connection-dot {
  background: #52c41a; /* 绿色表示已连接 */
  border-color: rgba(255, 255, 255, 1);
  animation: connectedPulse 2s ease-in-out infinite;
}

/* 已连接状态的脉冲动画 */
@keyframes connectedPulse {
  0%,
  100% {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 0 rgba(82, 196, 26, 0.4);
  }
  50% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15), 0 0 0 4px rgba(82, 196, 26, 0.2);
  }
}

/* 编辑状态下的连接点 - 隐藏但保留在DOM中 */
.connection-point.editing-hidden {
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
}

/* 连接点在不同便签颜色下的适配 */
.sticky-note.color-yellow .connection-point.connected .connection-dot,
.sticky-note.color-blue .connection-point.connected .connection-dot,
.sticky-note.color-green .connection-point.connected .connection-dot,
.sticky-note.color-pink .connection-point.connected .connection-dot,
.sticky-note.color-purple .connection-point.connected .connection-dot {
  background: #52c41a; /* 统一使用绿色表示连接状态 */
}

/* 连接点有溯源时的额外样式调整 - 保留用于向后兼容 */
.connection-point.has-source {
  /* 有溯源标识的连接点可以有稍微不同的边框 */
  border-color: rgba(250, 140, 22, 0.1); /* 非常淡的橙色边框提示 */
}

/* 有溯源标识的连接点悬停时的特殊效果 */
.connection-point.has-source:hover {
  border-color: rgba(250, 140, 22, 0.3); /* 悬停时橙色边框更明显 */
}

/* 分页Markdown容器样式 */
.paginated-markdown-container {
  width: 100%;
  height: 100%;
  user-select: text; /* 允许选择分页Markdown容器中的文本 */
}

/* 分页Markdown容器中的分割线样式 */
.paginated-markdown-container hr {
  border: none;
  height: 1px;
  background: rgba(0, 0, 0, 0.15);
  margin: 16px 0;
}
