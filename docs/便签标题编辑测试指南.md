# 便签标题编辑功能测试指南

## 🚀 快速测试

### 启动应用

```bash
cd /Users/<USER>/个人/infinity-notes_1
npm run dev
```

然后访问：http://localhost:5173/app.html

### 测试步骤

#### 1. 基础功能测试

1. **创建便签**：双击画布空白处创建新便签
2. **编辑标题**：双击便签标题进入编辑模式
3. **输入长标题**：尝试输入很长的标题文本
4. **调整便签宽度**：拖拽便签右下角调整宽度
5. **观察适应性**：查看标题输入框是否正确适应宽度变化

#### 2. 极限测试

1. **最小宽度**：将便签缩小到最小宽度，测试标题编辑
2. **超长标题**：输入超过 100 个字符的标题
3. **特殊字符**：测试 emoji、中英文混合、特殊符号
4. **快捷键**：测试 Enter 保存、Escape 取消

#### 3. 布局测试

1. **多个便签**：创建多个不同宽度的便签
2. **同时编辑**：测试多个便签的标题编辑状态
3. **缩放测试**：在不同画布缩放级别下测试

## 🎯 预期效果

### ✅ 应该看到的改进

- 标题输入框不会超出便签边界
- 输入框宽度随便签宽度自适应
- 超长文本显示省略号
- 最小宽度保护（80px）
- 与删除按钮保持合理间距

### ❌ 不应该出现的问题

- 标题输入框溢出容器
- 输入框被删除按钮遮挡
- 在窄便签中无法编辑标题
- 样式错乱或显示异常

## 🧪 独立测试页面

### 基础功能测试

```
http://localhost:8080/public/title-edit-test.html
```

### 状态切换测试 (新增)

```
http://localhost:8080/public/title-state-test.html
```

专门测试编辑状态与显示状态之间切换时的布局一致性：

- 编辑状态与显示状态的宽度对比
- 不同便签宽度下的表现
- 超长标题的处理效果
- 状态切换的视觉一致性

## 📝 测试要点

### 关键测试场景

1. **窄便签 (200-250px)**：确保最小可用性
2. **标准便签 (300-400px)**：验证正常使用体验
3. **宽便签 (500px+)**：测试大容量显示
4. **超长标题**：验证溢出处理

### 交互测试

- 双击开始编辑 ✓
- 单击其他区域保存 ✓
- Enter 键保存 ✓
- Escape 键取消 ✓
- Tab 键插入制表符 ✓

### 样式测试

- 背景色正确 ✓
- 字体粗细一致 ✓
- 边距合理 ✓
- 边框圆角 ✓
- 焦点状态 ✓

## 🐛 问题报告

如果发现问题，请记录：

1. **便签宽度**：问题出现时的便签宽度
2. **标题长度**：问题标题的字符数
3. **浏览器信息**：浏览器类型和版本
4. **操作步骤**：复现问题的具体步骤
5. **截图**：问题的视觉表现

## 🔧 调试提示

### 开发者工具检查

```css
/* 检查这些CSS类是否正确应用 */
.sticky-note
  .ant-input.ant-input-sm.ant-input-borderless
  .sticky-note-title-input
  .sticky-note-header;
```

### 控制台验证

```javascript
// 检查标题输入框的计算样式
const titleInput = document.querySelector(".ant-input-borderless");
console.log(getComputedStyle(titleInput));
```
